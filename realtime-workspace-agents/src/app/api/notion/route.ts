// app/api/notion/route.ts
import { NextResponse } from "next/server";
import { NotionAPI } from "notion-client";

// const notion = new Client({ auth: process.env.NOTION_INTEGRATION_TOKEN });

export async function GET() {
  try {
    // const response = await notion.databases.query({ database_id: process.env.NOTION_DATABASE_ID!, });
    const defaultPageId = "274a860b701080368183ce1111e68d65";
    const notion = new NotionAPI({
      // authToken: "**************************************************",
    });
    console.log({ notion, page: notion.getPage, defaultPageId });
    const data = await notion.getPage(defaultPageId);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error(error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
